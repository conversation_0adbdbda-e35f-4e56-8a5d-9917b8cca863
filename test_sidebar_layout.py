#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试侧边栏布局优化 - 验证分析选项和聊天管理功能移动到左侧导航栏
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import json

def test_sidebar_layout():
    """测试侧边栏布局优化"""
    
    st.set_page_config(
        page_title="侧边栏布局测试",
        page_icon="🧪",
        layout="centered",
        initial_sidebar_state="expanded"
    )
    
    st.title("🧪 侧边栏布局优化测试")
    
    # 初始化测试数据
    if 'test_chat_messages' not in st.session_state:
        st.session_state.test_chat_messages = [
            {
                "role": "assistant",
                "content": "您好！我是您的AI数据分析助手。请告诉我您想要分析什么？",
                "timestamp": "2025-01-05 10:00:00"
            },
            {
                "role": "user",
                "content": "请分析销售数据的基本统计信息",
                "timestamp": "2025-01-05 10:01:00"
            },
            {
                "role": "assistant",
                "content": "✅ 分析完成！我已经根据您的要求生成并执行了相应的代码。",
                "timestamp": "2025-01-05 10:01:15",
                "code": "df.describe()",
                "exec_result": {"success": True}
            },
            {
                "role": "user",
                "content": "请绘制销售趋势图",
                "timestamp": "2025-01-05 10:02:00"
            },
            {
                "role": "assistant",
                "content": "✅ 分析完成！我已经根据您的要求生成并执行了相应的代码。",
                "timestamp": "2025-01-05 10:02:20",
                "code": "plt.plot(df['date'], df['sales'])\nplt.title('销售趋势')\nplt.show()",
                "exec_result": {"success": True}
            }
        ]
    
    if 'test_metadata_enabled' not in st.session_state:
        st.session_state.test_metadata_enabled = False
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 系统配置")
        
        # 系统状态
        st.markdown("### 📊 系统状态")
        st.success("🤖 LLM: 已就绪")
        st.success("📊 数据: 已加载")
        st.info("📁 文件: test_sales_data.csv")
        st.info("📏 形状: (100, 5)")
        
        st.markdown("---")
        
        # 分析选项设置
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.subheader("⚙️ 分析选项")
        st.session_state.test_metadata_enabled = st.checkbox(
            "默认使用元数据增强",
            value=st.session_state.test_metadata_enabled,
            help="使用数据元数据提供更精确的分析"
        )
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown("---")
        
        # 聊天管理功能
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.subheader("💬 聊天管理")
        
        # 聊天操作按钮
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ 清空聊天", use_container_width=True, help="清空当前聊天记录"):
                welcome_message = {
                    "role": "assistant", 
                    "content": "您好！我是您的AI数据分析助手。请告诉我您想要分析什么？",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                st.session_state.test_chat_messages = [welcome_message]
                st.rerun()
        
        with col2:
            if st.button("📤 导出聊天", use_container_width=True, help="导出聊天记录为JSON文件"):
                if st.session_state.test_chat_messages:
                    # 创建导出数据
                    export_data = {
                        "export_time": datetime.now().isoformat(),
                        "total_messages": len(st.session_state.test_chat_messages),
                        "messages": st.session_state.test_chat_messages
                    }
                    
                    # 转换为JSON字符串
                    json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
                    
                    # 提供下载
                    st.download_button(
                        label="⬇️ 下载JSON文件",
                        data=json_str,
                        file_name=f"test_chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json",
                        use_container_width=True
                    )
        
        # 显示聊天统计
        if st.session_state.test_chat_messages:
            messages = st.session_state.test_chat_messages
            user_msgs = [m for m in messages if m["role"] == "user"]
            assistant_msgs = [m for m in messages if m["role"] == "assistant"]
            
            st.write("📊 **聊天统计:**")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("总消息", len(messages))
            with col2:
                st.metric("用户问题", len(user_msgs))
            with col3:
                st.metric("AI回复", len(assistant_msgs))
            
            # 显示最近的问题
            if user_msgs:
                st.write("🔍 **最近的问题:**")
                for i, msg in enumerate(reversed(user_msgs[-3:])):  # 显示最近3个问题
                    with st.expander(f"问题 {len(user_msgs)-i}: {msg['content'][:30]}...", expanded=False):
                        st.write(f"**时间:** {msg.get('timestamp', '未知')}")
                        st.write(f"**完整问题:** {msg['content']}")
                        
                        # 找到对应的回答
                        msg_idx = messages.index(msg)
                        if msg_idx + 1 < len(messages) and messages[msg_idx + 1]["role"] == "assistant":
                            answer = messages[msg_idx + 1]
                            st.write(f"**AI回答:** {answer['content'][:150]}...")
                            if answer.get('code'):
                                st.code(answer['code'][:200] + "..." if len(answer['code']) > 200 else answer['code'], language='python')
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown("---")
        
        # 全屏按钮
        if st.button("🔍 全屏模式", help="进入全屏模式进行数据分析", use_container_width=True):
            st.success("全屏模式已激活！")
    
    # 主界面 - 简洁的聊天界面
    st.markdown("## 💬 AI数据分析聊天")
    st.info("✨ **界面优化完成！** 分析选项和聊天管理功能已移动到左侧导航栏，主界面更加简洁。")
    
    # 显示聊天历史
    for message in st.session_state.test_chat_messages:
        with st.chat_message(message["role"]):
            if message.get("timestamp"):
                st.caption(f"🕒 {message['timestamp']}")
            st.markdown(message["content"])
            
            # 如果是助手消息且包含代码，显示代码
            if message["role"] == "assistant" and message.get("code"):
                with st.expander("📝 查看生成的代码", expanded=False):
                    st.code(message["code"], language='python')
                
                # 模拟执行结果
                if message.get("exec_result", {}).get("success"):
                    st.markdown("📊 **执行结果：**")
                    if "describe" in message.get("code", ""):
                        # 模拟统计结果
                        st.subheader("数据统计信息")
                        sample_stats = pd.DataFrame({
                            'sales': [100, 200, 150, 50],
                            'profit': [10, 20, 15, 5]
                        }).describe()
                        st.dataframe(sample_stats, use_container_width=True)
                    elif "plot" in message.get("code", ""):
                        # 模拟图表
                        st.subheader("销售趋势图")
                        chart_data = pd.DataFrame({
                            'date': pd.date_range('2024-01-01', periods=30),
                            'sales': np.random.randint(100, 500, 30)
                        })
                        st.line_chart(chart_data.set_index('date'))
    
    # 聊天输入
    if prompt := st.chat_input("请输入您的分析需求..."):
        # 添加用户消息
        user_message = {
            "role": "user",
            "content": prompt,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        st.session_state.test_chat_messages.append(user_message)
        
        # 添加AI回复
        ai_message = {
            "role": "assistant",
            "content": f"✅ 分析完成！我已经根据您的要求「{prompt}」生成并执行了相应的代码。",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "code": f"# 根据您的要求: {prompt}\nprint('分析结果')",
            "exec_result": {"success": True}
        }
        st.session_state.test_chat_messages.append(ai_message)
        
        st.rerun()
    
    # 显示当前配置状态
    st.markdown("---")
    st.markdown("### 📋 当前配置状态")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**元数据增强:** {'✅ 启用' if st.session_state.test_metadata_enabled else '❌ 禁用'}")
    with col2:
        st.write(f"**聊天消息数:** {len(st.session_state.test_chat_messages)}")
    
    st.success("🎉 **布局优化测试完成！** 所有功能都已成功移动到左侧导航栏。")

if __name__ == "__main__":
    test_sidebar_layout()
