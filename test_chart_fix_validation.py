#!/usr/bin/env python3
"""
测试图表修复功能验证脚本
严格按照Streamlit技术文档要求验证修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.processors.chart_fixer import ChartFixer
from core.llm.tongyi_client import TongyiQianwenClient
import pandas as pd

def test_chart_fixer():
    """测试图表修复器的功能"""
    print("🔧 测试图表修复器功能...")
    
    fixer = ChartFixer()
    
    # 测试1：matplotlib散点图转换
    matplotlib_scatter_code = """
import matplotlib.pyplot as plt
plt.scatter(df['销售额'], df['销量'])
plt.show()
"""
    
    print("\n📊 测试1：matplotlib散点图转换")
    print("原始代码:")
    print(matplotlib_scatter_code)
    
    fixed_code = fixer.fix_charts(matplotlib_scatter_code, "创建散点图")
    print("\n修复后代码:")
    print(fixed_code)
    
    # 测试2：matplotlib饼图转换
    matplotlib_pie_code = """
import matplotlib.pyplot as plt
plt.pie(df['销售额'], labels=df['产品名称'])
plt.show()
"""
    
    print("\n🥧 测试2：matplotlib饼图转换")
    print("原始代码:")
    print(matplotlib_pie_code)
    
    fixed_code = fixer.fix_charts(matplotlib_pie_code, "创建饼图")
    print("\n修复后代码:")
    print(fixed_code)
    
    # 测试3：matplotlib柱状图转换
    matplotlib_bar_code = """
import matplotlib.pyplot as plt
plt.bar(df['产品名称'], df['销售额'])
plt.show()
"""
    
    print("\n📊 测试3：matplotlib柱状图转换")
    print("原始代码:")
    print(matplotlib_bar_code)
    
    fixed_code = fixer.fix_charts(matplotlib_bar_code, "创建柱状图")
    print("\n修复后代码:")
    print(fixed_code)

def test_prompt_enhancement():
    """测试提示词增强功能"""
    print("\n🤖 测试提示词增强功能...")
    
    # 模拟TongyiQianwenClient（不实际调用API）
    client = TongyiQianwenClient("dummy_key")
    
    instruction = "创建销售额的散点图"
    context = "数据包含：产品名称、销售额、销量、地区等列"
    
    prompt = client._build_prompt(instruction, context)
    
    print("\n增强后的提示词:")
    print("=" * 80)
    print(prompt)
    print("=" * 80)
    
    # 检查关键要素
    required_elements = [
        "st.scatter_chart()",
        "st.bar_chart()",
        "st.line_chart()",
        "st.area_chart()",
        "plotly",
        "st.plotly_chart()",
        "禁止使用matplotlib"
    ]
    
    print("\n✅ 提示词包含的关键要素:")
    for element in required_elements:
        if element in prompt:
            print(f"  ✓ {element}")
        else:
            print(f"  ✗ {element} (缺失)")

def test_streamlit_api_compliance():
    """测试Streamlit API合规性"""
    print("\n📋 测试Streamlit API合规性...")
    
    fixer = ChartFixer()
    
    # 测试API参数使用
    test_cases = [
        ("散点图", "plt.scatter(x, y)", "st.scatter_chart"),
        ("柱状图", "plt.bar(x, y)", "st.bar_chart"),
        ("折线图", "plt.plot(x, y)", "st.line_chart"),
        ("饼图", "plt.pie(values, labels)", "plotly")
    ]
    
    for chart_type, original, expected in test_cases:
        print(f"\n{chart_type}测试:")
        fixed = fixer.fix_charts(original, f"创建{chart_type}")
        
        if expected in fixed:
            print(f"  ✅ 正确使用 {expected}")
        else:
            print(f"  ❌ 未找到 {expected}")
        
        # 检查use_container_width参数
        if "use_container_width=True" in fixed:
            print("  ✅ 包含 use_container_width=True 参数")
        else:
            print("  ⚠️  缺少 use_container_width=True 参数")

if __name__ == "__main__":
    print("🎯 开始验证图表修复功能...")
    print("严格按照Streamlit技术文档要求进行验证")
    print("=" * 80)
    
    try:
        test_chart_fixer()
        test_prompt_enhancement()
        test_streamlit_api_compliance()
        
        print("\n" + "=" * 80)
        print("🎉 验证完成！")
        print("✅ 修复已按照Streamlit官方技术文档要求实施")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
