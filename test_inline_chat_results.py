#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试聊天消息中内联显示执行结果的功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def test_inline_chat_display():
    """测试在聊天消息中内联显示结果"""
    
    st.title("🧪 聊天内联结果显示测试")
    
    # 创建测试数据
    if 'test_data' not in st.session_state:
        np.random.seed(42)
        st.session_state.test_data = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=100),
            'sales': np.random.randint(100, 1000, 100),
            'profit': np.random.randint(10, 100, 100),
            'region': np.random.choice(['North', 'South', 'East', 'West'], 100)
        })
    
    df = st.session_state.test_data
    
    st.write("📊 **测试数据预览:**")
    st.dataframe(df.head(), use_container_width=True)
    
    st.markdown("---")
    
    # 模拟聊天对话
    st.header("💬 模拟聊天对话效果")
    
    # 用户问题1
    with st.chat_message("user"):
        st.caption("🕒 2025-01-05 10:30:00")
        st.markdown("请分析销售数据的基本统计信息")
    
    # AI回答1 - 包含表格结果
    with st.chat_message("assistant"):
        st.caption("🕒 2025-01-05 10:30:15")
        st.markdown("✅ **分析完成！我已经根据您的要求生成并执行了相应的代码。**")
        
        with st.expander("📝 查看生成的代码", expanded=False):
            st.code("""
# 分析销售数据的基本统计信息
print("销售数据基本统计:")
stats = df[['sales', 'profit']].describe()
print(stats)

# 按地区统计
region_stats = df.groupby('region')[['sales', 'profit']].agg(['mean', 'sum'])
print("\\n按地区统计:")
print(region_stats)
""", language='python')
        
        st.markdown("📊 **执行结果：**")
        
        # 直接显示统计结果
        st.subheader("销售数据基本统计")
        stats = df[['sales', 'profit']].describe()
        st.dataframe(stats, use_container_width=True)
        
        st.subheader("按地区统计")
        region_stats = df.groupby('region')[['sales', 'profit']].agg(['mean', 'sum'])
        st.dataframe(region_stats, use_container_width=True)
    
    # 用户问题2
    with st.chat_message("user"):
        st.caption("🕒 2025-01-05 10:32:00")
        st.markdown("请绘制销售趋势图")
    
    # AI回答2 - 包含图表结果
    with st.chat_message("assistant"):
        st.caption("🕒 2025-01-05 10:32:20")
        st.markdown("✅ **分析完成！我已经根据您的要求生成并执行了相应的代码。**")
        
        with st.expander("📝 查看生成的代码", expanded=False):
            st.code("""
import matplotlib.pyplot as plt

# 创建销售趋势图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

# 销售趋势
ax1.plot(df['date'], df['sales'], color='blue', linewidth=2)
ax1.set_title('销售趋势', fontsize=14, fontweight='bold')
ax1.set_ylabel('销售额')
ax1.grid(True, alpha=0.3)

# 利润趋势
ax2.plot(df['date'], df['profit'], color='green', linewidth=2)
ax2.set_title('利润趋势', fontsize=14, fontweight='bold')
ax2.set_xlabel('日期')
ax2.set_ylabel('利润')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
""", language='python')
        
        st.markdown("📊 **执行结果：**")
        
        # 直接显示图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # 销售趋势
        ax1.plot(df['date'], df['sales'], color='blue', linewidth=2)
        ax1.set_title('销售趋势', fontsize=14, fontweight='bold')
        ax1.set_ylabel('销售额')
        ax1.grid(True, alpha=0.3)
        
        # 利润趋势
        ax2.plot(df['date'], df['profit'], color='green', linewidth=2)
        ax2.set_title('利润趋势', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('利润')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        st.pyplot(fig, use_container_width=True)
    
    # 用户问题3
    with st.chat_message("user"):
        st.caption("🕒 2025-01-05 10:35:00")
        st.markdown("请分析各地区的销售分布")
    
    # AI回答3 - 包含多种图表
    with st.chat_message("assistant"):
        st.caption("🕒 2025-01-05 10:35:30")
        st.markdown("✅ **分析完成！我已经根据您的要求生成并执行了相应的代码。**")
        
        with st.expander("📝 查看生成的代码", expanded=False):
            st.code("""
import matplotlib.pyplot as plt

# 创建地区销售分析图
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

# 1. 各地区总销售额
region_sales = df.groupby('region')['sales'].sum()
ax1.bar(region_sales.index, region_sales.values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
ax1.set_title('各地区总销售额', fontweight='bold')
ax1.set_ylabel('销售额')

# 2. 各地区平均利润
region_profit = df.groupby('region')['profit'].mean()
ax2.pie(region_profit.values, labels=region_profit.index, autopct='%1.1f%%', 
        colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
ax2.set_title('各地区平均利润分布', fontweight='bold')

# 3. 销售额箱线图
df.boxplot(column='sales', by='region', ax=ax3)
ax3.set_title('各地区销售额分布', fontweight='bold')
ax3.set_xlabel('地区')

# 4. 利润散点图
for region in df['region'].unique():
    region_data = df[df['region'] == region]
    ax4.scatter(region_data['sales'], region_data['profit'], 
               label=region, alpha=0.6, s=50)
ax4.set_xlabel('销售额')
ax4.set_ylabel('利润')
ax4.set_title('销售额与利润关系', fontweight='bold')
ax4.legend()

plt.tight_layout()
plt.show()
""", language='python')
        
        st.markdown("📊 **执行结果：**")
        
        # 直接显示复合图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 各地区总销售额
        region_sales = df.groupby('region')['sales'].sum()
        ax1.bar(region_sales.index, region_sales.values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax1.set_title('各地区总销售额', fontweight='bold')
        ax1.set_ylabel('销售额')
        
        # 2. 各地区平均利润
        region_profit = df.groupby('region')['profit'].mean()
        ax2.pie(region_profit.values, labels=region_profit.index, autopct='%1.1f%%', 
                colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax2.set_title('各地区平均利润分布', fontweight='bold')
        
        # 3. 销售额箱线图
        df.boxplot(column='sales', by='region', ax=ax3)
        ax3.set_title('各地区销售额分布', fontweight='bold')
        ax3.set_xlabel('地区')
        
        # 4. 利润散点图
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        for i, region in enumerate(df['region'].unique()):
            region_data = df[df['region'] == region]
            ax4.scatter(region_data['sales'], region_data['profit'], 
                       label=region, alpha=0.6, s=50, color=colors[i])
        ax4.set_xlabel('销售额')
        ax4.set_ylabel('利润')
        ax4.set_title('销售额与利润关系', fontweight='bold')
        ax4.legend()
        
        plt.tight_layout()
        st.pyplot(fig, use_container_width=True)
        
        # 额外显示数据表格
        st.subheader("地区统计汇总")
        summary = df.groupby('region').agg({
            'sales': ['count', 'mean', 'sum'],
            'profit': ['mean', 'sum']
        }).round(2)
        st.dataframe(summary, use_container_width=True)
    
    st.markdown("---")
    st.success("✅ **测试完成！** 这就是用户期望的聊天体验：每个问题下面直接显示对应的分析结果，包括图表、表格等。")

if __name__ == "__main__":
    test_inline_chat_display()
