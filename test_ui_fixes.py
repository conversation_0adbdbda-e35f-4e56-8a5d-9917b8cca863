#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_chat_message_styles():
    """测试聊天消息样式修复"""
    print("🔍 测试聊天消息样式修复...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查聊天消息样式修复
            if ".stChatMessage" in content and "!important" in content:
                print("✅ 聊天消息样式修复代码已添加")
            else:
                print("❌ 聊天消息样式修复代码未找到")
                return False
            
            # 检查用户消息样式
            if 'data-testid="user-message"' in content:
                print("✅ 用户消息样式已定义")
            else:
                print("❌ 用户消息样式未找到")
                return False
            
            # 检查助手消息样式
            if 'data-testid="assistant-message"' in content:
                print("✅ 助手消息样式已定义")
            else:
                print("❌ 助手消息样式未找到")
                return False
            
            return True
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 聊天消息样式测试失败: {e}")
        return False

def test_notification_removal():
    """测试提示信息移除"""
    print("\n🔍 测试提示信息移除...")
    
    try:
        # 检查自动加载文件提示移除
        integration_file = Path("core/integrations/streamlit_integration.py")
        if integration_file.exists():
            content = integration_file.read_text(encoding='utf-8')
            
            if "🔄 已自动加载最近文件" not in content:
                print("✅ 自动加载文件提示已移除")
            else:
                print("❌ 自动加载文件提示仍然存在")
                return False
        
        # 检查文本列提示移除
        validators_file = Path("core/utils/validators.py")
        if validators_file.exists():
            content = validators_file.read_text(encoding='utf-8')
            
            if "包含文本列:" not in content:
                print("✅ 文本列提示已移除")
            else:
                print("❌ 文本列提示仍然存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 提示信息移除测试失败: {e}")
        return False

def test_css_improvements():
    """测试CSS改进"""
    print("\n🔍 测试CSS改进...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查聊天输入框样式
            if ".stChatInput" in content:
                print("✅ 聊天输入框样式已添加")
            else:
                print("❌ 聊天输入框样式未找到")
                return False
            
            # 检查文本颜色修复
            if "color: #333333 !important" in content:
                print("✅ 文本颜色修复已添加")
            else:
                print("❌ 文本颜色修复未找到")
                return False
            
            # 检查背景色修复
            if "background-color: #ffffff !important" in content:
                print("✅ 背景色修复已添加")
            else:
                print("❌ 背景色修复未找到")
                return False
            
            return True
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ CSS改进测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试UI修复效果\n")
    
    tests = [
        ("聊天消息样式修复", test_chat_message_styles),
        ("提示信息移除", test_notification_removal),
        ("CSS改进", test_css_improvements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 UI修复测试通过！")
        print("\n📋 修复内容清单:")
        print("✅ 聊天消息白色背景问题 - 已修复")
        print("✅ 自动加载文件提示 - 已移除")
        print("✅ 文本列提示信息 - 已移除")
        print("✅ 聊天消息文本颜色 - 已优化")
        print("✅ 用户/助手消息区分 - 已增强")
        
        print("\n🚀 现在可以访问: http://localhost:8501")
        print("💡 改进效果:")
        print("   - 聊天记录在全屏模式下正常显示")
        print("   - 移除了不必要的提示信息")
        print("   - 优化了消息的视觉效果")
        print("   - 增强了用户和AI消息的区分度")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
