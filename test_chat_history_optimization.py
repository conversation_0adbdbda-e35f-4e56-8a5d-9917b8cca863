#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试聊天历史优化功能
"""

import streamlit as st
import json
from datetime import datetime
from pathlib import Path

def test_chat_history_functions():
    """测试聊天历史相关功能"""
    
    st.title("🧪 聊天历史功能测试")
    
    # 测试消息格式
    st.header("1. 测试消息格式")
    
    sample_messages = [
        {
            "role": "user",
            "content": "请分析销售数据的趋势",
            "timestamp": "2025-01-05 10:30:00"
        },
        {
            "role": "assistant",
            "content": "✅ 分析完成！我已经根据您的要求生成并执行了相应的代码。",
            "timestamp": "2025-01-05 10:30:15",
            "code": "import pandas as pd\nimport matplotlib.pyplot as plt\n\n# 分析销售趋势\ndf_trend = df.groupby('date')['sales'].sum()\ndf_trend.plot(kind='line')\nplt.title('销售趋势分析')\nplt.show()",
            "exec_result": {
                "success": True,
                "error": None
            }
        }
    ]
    
    # 显示示例消息
    for msg in sample_messages:
        with st.chat_message(msg["role"]):
            # 显示时间戳
            if msg.get("timestamp"):
                st.caption(f"🕒 {msg['timestamp']}")
            
            # 显示消息内容
            st.markdown(msg["content"])
            
            # 如果是助手消息且包含代码，显示代码
            if msg["role"] == "assistant" and msg.get("code"):
                with st.expander("📝 查看生成的代码", expanded=False):
                    st.code(msg["code"], language='python')
            
            # 如果有执行结果，显示结果
            if msg.get("exec_result"):
                exec_result = msg["exec_result"]
                with st.expander("📊 执行结果详情", expanded=False):
                    if exec_result.get("success"):
                        st.success("✅ 代码执行成功")
                    else:
                        st.error(f"❌ 执行失败: {exec_result.get('error', '未知错误')}")
    
    # 测试聊天历史文件操作
    st.header("2. 测试聊天历史文件操作")
    
    # 创建测试目录
    chat_dir = Path("chat_history")
    chat_dir.mkdir(exist_ok=True)
    
    # 保存测试数据
    if st.button("💾 保存测试聊天历史"):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = chat_dir / f"chat_test_{timestamp}.json"
        
        test_data = {
            "export_time": datetime.now().isoformat(),
            "total_messages": len(sample_messages),
            "messages": sample_messages
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            st.success(f"✅ 测试聊天历史已保存: {filename.name}")
        except Exception as e:
            st.error(f"❌ 保存失败: {e}")
    
    # 列出现有文件
    if chat_dir.exists():
        files = list(chat_dir.glob("chat_*.json"))
        if files:
            st.write(f"📂 找到 {len(files)} 个聊天历史文件:")
            for file in files[-5:]:  # 显示最近5个文件
                file_info = file.stat()
                file_time = datetime.fromtimestamp(file_info.st_mtime)
                st.write(f"- {file.name} ({file_time.strftime('%Y-%m-%d %H:%M:%S')})")
    
    # 测试搜索功能
    st.header("3. 测试搜索功能")
    
    search_term = st.text_input("搜索测试消息", value="销售")
    if search_term:
        matching_messages = []
        for i, message in enumerate(sample_messages):
            if search_term.lower() in message["content"].lower():
                matching_messages.append((i, message))
        
        if matching_messages:
            st.success(f"✅ 找到 {len(matching_messages)} 条匹配消息:")
            for i, (idx, message) in enumerate(matching_messages):
                with st.expander(f"消息 #{idx} - {message['role']}", expanded=True):
                    st.write(f"**时间:** {message.get('timestamp', '未知')}")
                    st.write(f"**内容:** {message['content']}")
        else:
            st.info("未找到匹配的消息")
    
    # 测试统计功能
    st.header("4. 测试统计功能")
    
    user_msgs = [m for m in sample_messages if m["role"] == "user"]
    assistant_msgs = [m for m in sample_messages if m["role"] == "assistant"]
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总消息", len(sample_messages))
    with col2:
        st.metric("用户问题", len(user_msgs))
    with col3:
        st.metric("AI回复", len(assistant_msgs))
    
    # 导出功能测试
    st.header("5. 测试导出功能")
    
    export_data = {
        "export_time": datetime.now().isoformat(),
        "total_messages": len(sample_messages),
        "messages": sample_messages
    }
    
    json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
    
    st.download_button(
        label="⬇️ 下载测试聊天记录",
        data=json_str,
        file_name=f"test_chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )
    
    # 显示JSON预览
    with st.expander("📄 JSON数据预览", expanded=False):
        st.json(export_data)

if __name__ == "__main__":
    test_chat_history_functions()
