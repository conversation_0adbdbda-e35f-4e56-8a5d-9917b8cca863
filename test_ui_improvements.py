#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI改进功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ui_improvements():
    """测试UI改进功能"""
    print("🔍 测试UI改进功能...")
    
    try:
        from app.main import main
        print("✅ 主应用模块导入成功")
        
        # 检查主要文件是否存在
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查全屏相关代码
            if "main-content-full" in content:
                print("✅ 全屏布局代码已添加")
            else:
                print("❌ 全屏布局代码未找到")
                return False
            
            # 检查悬浮窗相关代码
            if "status-float" in content:
                print("✅ 悬浮状态窗代码已添加")
            else:
                print("❌ 悬浮状态窗代码未找到")
                return False
            
            # 检查JavaScript代码
            if "requestFullscreen" in content:
                print("✅ 全屏JavaScript代码已添加")
            else:
                print("❌ 全屏JavaScript代码未找到")
                return False
            
            # 检查CSS样式
            if ".status-float" in content:
                print("✅ 悬浮窗CSS样式已添加")
            else:
                print("❌ 悬浮窗CSS样式未找到")
                return False
            
            return True
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ UI改进测试失败: {e}")
        return False

def test_config_changes():
    """测试配置更改"""
    print("\n🔍 测试配置更改...")
    
    try:
        from config.app_settings import get_config
        config = get_config()
        
        # 检查Streamlit配置
        if hasattr(config, 'streamlit'):
            if config.streamlit.layout == "wide":
                print("✅ Streamlit布局配置为wide")
            else:
                print("❌ Streamlit布局配置不正确")
                return False
            
            if config.streamlit.initial_sidebar_state == "collapsed":
                print("✅ 侧边栏默认折叠配置正确")
            else:
                print("❌ 侧边栏配置不正确")
                return False
            
            return True
        else:
            print("❌ Streamlit配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试UI改进功能\n")
    
    tests = [
        ("UI改进功能", test_ui_improvements),
        ("配置更改", test_config_changes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 UI改进功能测试通过！")
        print("\n📋 改进功能清单:")
        print("✅ 页面全屏显示 - 自动隐藏工具栏，优化布局")
        print("✅ 悬浮状态窗 - 鼠标悬停显示，自动隐藏")
        print("✅ 全屏切换按钮 - 支持手动切换全屏模式")
        print("✅ 响应式设计 - 适配不同屏幕尺寸")
        
        print("\n🚀 现在可以访问: http://localhost:8501")
        print("💡 功能说明:")
        print("   - 页面会自动优化为全屏显示")
        print("   - 右上角悬浮窗显示系统状态")
        print("   - 鼠标移到悬浮窗可查看详细信息")
        print("   - 点击悬浮窗左上角按钮可切换全屏")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
