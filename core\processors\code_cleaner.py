"""
代码清理处理器 - 负责清理和修复生成的Python代码
"""

import re
import ast
from typing import List, Tuple
from ..utils.logger import get_app_logger
from ..utils.validators import validate_code


class CodeCleaner:
    """
    代码清理处理器
    
    负责清理LLM生成的Python代码，修复常见问题。
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化代码清理器
        
        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
    
    def clean(self, code: str) -> str:
        """
        清理生成的代码
        
        Args:
            code: 原始代码字符串
            
        Returns:
            清理后的代码字符串
        """
        if self.logger:
            self.logger.info("开始清理生成的代码")
        
        # 1. 移除markdown标记
        code = self._remove_markdown_markers(code)
        
        # 2. 清理行内容
        code = self._clean_lines(code)
        
        # 3. 修复缩进问题
        code = self._fix_indentation(code)

        # 4. 修复常见问题
        code = self.fix_common_issues(code)

        # 5. 验证代码
        is_valid, error_msg, warnings = validate_code(code)
        if not is_valid:
            if self.logger:
                self.logger.error(f"代码验证失败: {error_msg}")
            return f"# 代码生成失败: {error_msg}\nprint('代码生成失败')"
        
        if warnings and self.logger:
            for warning in warnings:
                self.logger.warning(f"代码警告: {warning}")
        
        if self.logger:
            self.logger.info("代码清理完成")
        
        return code
    
    def _remove_markdown_markers(self, code: str) -> str:
        """移除markdown代码块标记"""
        # 移除```python和```标记
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        return code.strip()
    
    def _clean_lines(self, code: str) -> str:
        """清理代码行"""
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            # 跳过空行
            if not line.strip():
                continue
            
            # 跳过中文解释行
            if self._contains_chinese_explanation(line.strip()):
                continue
            
            # 跳过中文注释行（但保留英文注释）
            if line.strip().startswith('#') and self._contains_chinese(line):
                continue
            
            clean_lines.append(line)
        
        return '\n'.join(clean_lines)
    
    def _contains_chinese_explanation(self, line: str) -> bool:
        """检查是否包含中文解释"""
        # 检查是否是纯中文解释行（不是代码）
        if not line.strip():
            return False
        
        # 如果包含Python关键字，可能是代码
        python_keywords = ['import', 'def', 'class', 'if', 'for', 'while', 'try', 'except', 'return', 'print']
        if any(keyword in line for keyword in python_keywords):
            return False
        
        # 如果包含中文且不包含代码特征，认为是解释
        return self._contains_chinese(line) and not any(char in line for char in ['=', '(', ')', '[', ']', '.'])
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        return any(ord(char) > 127 for char in text)
    
    def _fix_indentation(self, code: str) -> str:
        """修复代码缩进问题"""
        try:
            # 尝试解析代码，如果成功说明缩进正确
            ast.parse(code)
            return code
        except SyntaxError:
            # 缩进有问题，尝试修复
            return self._auto_fix_indentation(code)
    
    def _auto_fix_indentation(self, code: str) -> str:
        """自动修复缩进问题"""
        lines = code.split('\n')
        fixed_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
            
            # 检查是否需要减少缩进
            if stripped.startswith(('except', 'elif', 'else', 'finally')):
                indent_level = max(0, indent_level - 1)
            elif stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'with ')):
                # 这些语句后面需要增加缩进
                fixed_lines.append('    ' * indent_level + stripped)
                if stripped.endswith(':'):
                    indent_level += 1
                continue
            elif stripped.endswith(':'):
                # 其他以冒号结尾的语句
                fixed_lines.append('    ' * indent_level + stripped)
                indent_level += 1
                continue
            
            # 添加当前缩进
            fixed_lines.append('    ' * indent_level + stripped)
        
        return '\n'.join(fixed_lines)
    
    def fix_common_issues(self, code: str) -> str:
        """修复常见的代码问题"""
        # 1. 修复常见的变量名问题
        code = re.sub(r'\bdata\b(?!\[)', 'df', code)  # 将data替换为df（除非是data[...]）
        
        # 2. 确保pandas导入
        if 'pd.' in code and 'import pandas as pd' not in code:
            code = 'import pandas as pd\n' + code
        
        # 3. 确保numpy导入（如果需要）
        if 'np.' in code and 'import numpy as np' not in code:
            code = 'import numpy as np\n' + code
        
        # 4. 修复常见的方法调用问题
        code = re.sub(r'\.head\(\)', '.head()', code)
        code = re.sub(r'\.describe\(\)', '.describe()', code)
        
        return code
    
    def remove_dangerous_operations(self, code: str) -> str:
        """移除危险操作"""
        dangerous_patterns = [
            r'import\s+os',
            r'import\s+subprocess',
            r'import\s+sys',
            r'__import__',
            r'eval\s*\(',
            r'exec\s*\(',
            r'open\s*\(',
            r'file\s*\(',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                if self.logger:
                    self.logger.warning(f"移除危险操作: {pattern}")
                code = re.sub(pattern, '# 危险操作已移除', code, flags=re.IGNORECASE)
        
        return code
