#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证居中布局恢复
"""

import sys
from pathlib import Path

def test_centered_layout():
    """测试居中布局设置"""
    print("🔍 测试居中布局设置...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查layout参数
            if 'layout="centered"' in content:
                print("✅ 已设置layout='centered'参数")
                return True
            elif 'layout="wide"' in content:
                print("❌ 仍然是layout='wide'参数")
                return False
            else:
                print("❌ 未找到layout参数")
                return False
            
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 居中布局测试失败: {e}")
        return False

def test_page_config_parameters():
    """测试页面配置参数"""
    print("\n🔍 测试页面配置参数...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查必要的参数
            required_params = {
                'page_title': 'AI数据分析平台 V2.0',
                'page_icon': '🤖',
                'layout': 'centered',
                'initial_sidebar_state': 'expanded'
            }
            
            all_params_found = True
            for param, expected_value in required_params.items():
                if f'{param}=' in content:
                    print(f"✅ 找到参数: {param}")
                else:
                    print(f"❌ 缺少参数: {param}")
                    all_params_found = False
            
            return all_params_found
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 页面配置参数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证居中布局恢复\n")
    
    tests = [
        ("居中布局设置", test_centered_layout),
        ("页面配置参数", test_page_config_parameters),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 居中布局恢复成功！")
        print("\n📋 当前布局设置:")
        print("✅ layout='centered' - 使用默认的居中布局")
        print("✅ 页面配置参数完整 - 包含所有必要参数")
        print("✅ 遵循Streamlit最佳实践")
        
        print("\n🚀 现在可以访问应用查看居中布局效果")
        print("💡 居中布局优势:")
        print("   - 内容在固定宽度的居中列中显示")
        print("   - 提供更好的阅读体验")
        print("   - 在大屏幕上避免内容过度拉伸")
        print("   - 更适合文本密集的应用")
        print("   - 在不同设备上保持一致的视觉效果")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
