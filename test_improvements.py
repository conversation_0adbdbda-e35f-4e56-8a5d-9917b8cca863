#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit应用改进功能
"""

import sys
from pathlib import Path
import pandas as pd
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from app.main import main
        print("✅ 主应用模块导入成功")
    except Exception as e:
        print(f"❌ 主应用模块导入失败: {e}")
        return False
    
    try:
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        print("✅ Streamlit集成模块导入成功")
    except Exception as e:
        print(f"❌ Streamlit集成模块导入失败: {e}")
        return False
    
    try:
        from config.app_settings import get_config
        print("✅ 配置模块导入成功")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    return True

def test_integration_features():
    """测试集成功能"""
    print("\n🔍 测试集成功能...")
    
    try:
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        
        # 创建集成实例
        integration = StreamlitLLMIntegration(enable_logging=False)
        print("✅ 集成实例创建成功")
        
        # 测试HTML表格格式化
        test_df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['x', 'y', 'z'],
            'C': [1.1, 2.2, 3.3]
        })
        
        html_result = integration.format_dataframe_as_html(test_df)
        if 'custom-table' in html_result:
            print("✅ HTML表格格式化功能正常")
        else:
            print("❌ HTML表格格式化功能异常")
            return False
        
        # 测试自动初始化方法存在
        if hasattr(integration, 'auto_initialize_llm'):
            print("✅ 自动LLM初始化方法存在")
        else:
            print("❌ 自动LLM初始化方法不存在")
            return False
        
        # 测试自动文件加载方法存在
        if hasattr(integration, 'auto_load_recent_file'):
            print("✅ 自动文件加载方法存在")
        else:
            print("❌ 自动文件加载方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试配置...")
    
    try:
        from config.app_settings import get_config
        config = get_config()
        
        # 检查关键配置项
        if hasattr(config, 'streamlit'):
            print("✅ Streamlit配置存在")
        else:
            print("❌ Streamlit配置不存在")
            return False
        
        if hasattr(config, 'data'):
            print("✅ 数据配置存在")
        else:
            print("❌ 数据配置不存在")
            return False
        
        if hasattr(config, 'llm'):
            print("✅ LLM配置存在")
        else:
            print("❌ LLM配置不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def create_test_data():
    """创建测试数据文件"""
    print("\n🔍 创建测试数据...")
    
    try:
        from config.app_settings import get_config
        config = get_config()
        
        # 确保上传目录存在
        upload_dir = config.data.uploaded_files_dir
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试CSV文件
        test_data = pd.DataFrame({
            '销售额': [1000, 1500, 2000, 1200, 1800],
            '地区': ['北京', '上海', '广州', '深圳', '杭州'],
            '月份': ['1月', '2月', '3月', '4月', '5月'],
            '产品': ['A', 'B', 'A', 'C', 'B']
        })
        
        test_file = upload_dir / 'test_sales_data.csv'
        test_data.to_csv(test_file, index=False, encoding='utf-8')
        print(f"✅ 测试数据文件已创建: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Streamlit应用改进功能\n")
    
    tests = [
        ("模块导入", test_imports),
        ("集成功能", test_integration_features),
        ("配置系统", test_config),
        ("测试数据", create_test_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！应用已准备就绪。")
        print("\n📋 改进功能清单:")
        print("✅ LLM默认初始化 - 应用启动时自动初始化")
        print("✅ 自动文件加载 - 自动加载最近上传的文件")
        print("✅ HTML表格格式化 - 美观的表格显示")
        print("✅ 聊天式分析输出 - 集成到对话流中")
        print("✅ 图表显示优化 - 保持交互式状态")
        print("✅ UI布局优化 - 改进整体用户体验")
        
        print("\n🚀 启动应用:")
        print("streamlit run streamlit_app.py")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
