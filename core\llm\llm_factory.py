"""
LLM工厂类 - 负责创建和配置完整的LLM实例
"""

from typing import Optional, Dict, Any
from .tongyi_client import TongyiQianwenClient
from ..processors.code_cleaner import CodeCleaner
from ..processors.chart_fixer import ChartFixer
from ..processors.metadata_processor import MetadataProcessor
from ..utils.config import TongyiConfig
from ..utils.logger import get_app_logger


class EnhancedTongyiLLM:
    """
    增强的通义千问LLM
    
    组合了API客户端和各种处理器，提供完整的LLM功能。
    """
    
    def __init__(
        self,
        client: TongyiQianwenClient,
        code_cleaner: Optional[CodeCleaner] = None,
        chart_fixer: Optional[ChartFixer] = None,
        metadata_processor: Optional[MetadataProcessor] = None,
        enable_logging: bool = True
    ):
        """
        初始化增强LLM
        
        Args:
            client: 通义千问API客户端
            code_cleaner: 代码清理器
            chart_fixer: 图表修复器
            metadata_processor: 元数据处理器
            enable_logging: 是否启用日志记录
        """
        self.client = client
        self.code_cleaner = code_cleaner or CodeCleaner(enable_logging)
        self.chart_fixer = chart_fixer or ChartFixer(enable_logging)
        self.metadata_processor = metadata_processor or MetadataProcessor(enable_logging)
        
        self.logger = get_app_logger() if enable_logging else None
        
        # 功能开关
        self.enable_chart_fix = True
        self.enable_metadata = False
    
    def analyze_data(self, instruction: str, context: str, metadata: Optional[Dict] = None, table_name: str = "data") -> str:
        """
        分析数据并生成代码

        Args:
            instruction: 用户指令
            context: 数据上下文
            metadata: 元数据（可选）
            table_name: 表格名称，用于获取业务元数据

        Returns:
            处理后的Python代码
        """
        if self.logger:
            self.logger.info(f"开始分析数据 - 指令: {instruction[:50]}...")
        
        try:
            # 1. 构建提示词（传递表格名称）
            if self.enable_metadata:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, metadata, table_name)
            else:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, None, table_name)
            
            # 2. 调用LLM API
            response = self.client.call(instruction=prompt, context="")
            raw_code = response.content
            
            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")
            
            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)
            
            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code
            
            if self.logger:
                self.logger.info("数据分析完成")
            
            return final_code
            
        except Exception as e:
            error_msg = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return f"# {error_msg}\nst.error('{error_msg}')"
    
    def set_chart_fix_enabled(self, enabled: bool):
        """设置图表修复功能开关"""
        self.enable_chart_fix = enabled
        if self.logger:
            self.logger.info(f"图表修复功能: {'启用' if enabled else '禁用'}")
    
    def set_metadata_enabled(self, enabled: bool):
        """设置元数据功能开关"""
        self.enable_metadata = enabled
        if self.logger:
            self.logger.info(f"元数据功能: {'启用' if enabled else '禁用'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取LLM使用统计"""
        return self.client.get_stats()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.client.get_model_info()


class LLMFactory:
    """
    LLM工厂类
    
    负责创建和配置各种LLM实例。
    """
    
    @staticmethod
    def create_tongyi_llm(
        config: Optional[TongyiConfig] = None,
        enable_chart_fix: bool = True,
        enable_metadata: bool = False,
        enable_logging: bool = True
    ) -> EnhancedTongyiLLM:
        """
        创建增强的通义千问LLM实例
        
        Args:
            config: 通义千问配置（如果为None，从环境变量加载）
            enable_chart_fix: 是否启用图表修复
            enable_metadata: 是否启用元数据支持
            enable_logging: 是否启用日志记录
            
        Returns:
            配置好的增强LLM实例
        """
        # 如果没有提供配置，从环境变量加载
        if config is None:
            config = TongyiConfig.from_env()
        
        # 更新配置中的功能开关
        config.enable_chart_fix = enable_chart_fix
        config.enable_metadata = enable_metadata
        config.enable_logging = enable_logging
        
        # 创建API客户端
        client = TongyiQianwenClient(config)
        
        # 创建处理器
        code_cleaner = CodeCleaner(enable_logging)
        chart_fixer = ChartFixer(enable_logging)
        metadata_processor = MetadataProcessor(enable_logging)
        
        # 创建增强LLM
        enhanced_llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=code_cleaner,
            chart_fixer=chart_fixer,
            metadata_processor=metadata_processor,
            enable_logging=enable_logging
        )
        
        # 设置功能开关
        enhanced_llm.set_chart_fix_enabled(enable_chart_fix)
        enhanced_llm.set_metadata_enabled(enable_metadata)
        
        return enhanced_llm
    
    @staticmethod
    def create_basic_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建基础的通义千问LLM实例（最小功能）
        
        Args:
            config: 通义千问配置
            
        Returns:
            基础LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=False,
            enable_metadata=False,
            enable_logging=False
        )
    
    @staticmethod
    def create_full_featured_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建全功能的通义千问LLM实例
        
        Args:
            config: 通义千问配置
            
        Returns:
            全功能LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=True,
            enable_metadata=True,
            enable_logging=True
        )
    
    @staticmethod
    def get_available_models() -> Dict[str, Dict[str, Any]]:
        """获取可用的模型列表"""
        return TongyiQianwenClient.RECOMMENDED_MODELS
    
    @staticmethod
    def validate_config(config: TongyiConfig) -> tuple[bool, str]:
        """
        验证配置是否有效
        
        Args:
            config: 要验证的配置
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            client = TongyiQianwenClient(config)
            return client.validate_config(), ""
        except Exception as e:
            return False, str(e)
