#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宽屏布局实现
"""

import sys
from pathlib import Path

def test_page_config_position():
    """测试st.set_page_config的位置是否正确"""
    print("🔍 测试st.set_page_config位置...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            # 查找st.set_page_config的位置
            config_line = -1
            first_streamlit_command = -1
            
            for i, line in enumerate(lines):
                if 'st.set_page_config' in line:
                    config_line = i + 1
                elif line.strip().startswith('st.') and 'st.set_page_config' not in line and first_streamlit_command == -1:
                    first_streamlit_command = i + 1
            
            if config_line == -1:
                print("❌ 未找到st.set_page_config")
                return False
            
            # 检查是否在文件开头附近
            if config_line < 20:
                print(f"✅ st.set_page_config在第{config_line}行，位置正确")
            else:
                print(f"❌ st.set_page_config在第{config_line}行，位置太靠后")
                return False
            
            # 检查layout参数
            if 'layout="wide"' in content:
                print("✅ 已设置layout='wide'参数")
            else:
                print("❌ 未设置layout='wide'参数")
                return False
            
            return True
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 页面配置测试失败: {e}")
        return False

def test_wide_layout_parameters():
    """测试宽屏布局参数"""
    print("\n🔍 测试宽屏布局参数...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 检查必要的参数
            required_params = {
                'page_title': 'AI数据分析平台 V2.0',
                'page_icon': '🤖',
                'layout': 'wide',
                'initial_sidebar_state': 'expanded'
            }
            
            all_params_found = True
            for param, expected_value in required_params.items():
                if f'{param}=' in content:
                    print(f"✅ 找到参数: {param}")
                else:
                    print(f"❌ 缺少参数: {param}")
                    all_params_found = False
            
            return all_params_found
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 宽屏布局参数测试失败: {e}")
        return False

def test_no_duplicate_config():
    """测试是否有重复的配置调用"""
    print("\n🔍 测试是否有重复的配置调用...")
    
    try:
        main_file = Path("app/main.py")
        if main_file.exists():
            content = main_file.read_text(encoding='utf-8')
            
            # 计算st.set_page_config的出现次数
            config_count = content.count('st.set_page_config')
            
            if config_count == 1:
                print("✅ 只有一个st.set_page_config调用")
                return True
            elif config_count > 1:
                print(f"❌ 发现{config_count}个st.set_page_config调用，应该只有一个")
                return False
            else:
                print("❌ 未找到st.set_page_config调用")
                return False
            
        else:
            print("❌ 主应用文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 重复配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试宽屏布局实现\n")
    
    tests = [
        ("页面配置位置", test_page_config_position),
        ("宽屏布局参数", test_wide_layout_parameters),
        ("无重复配置", test_no_duplicate_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 宽屏布局实现测试通过！")
        print("\n📋 实现内容清单:")
        print("✅ st.set_page_config位置正确 - 在文件开头")
        print("✅ layout='wide'参数已设置 - 使用整个屏幕宽度")
        print("✅ 页面配置参数完整 - 包含所有必要参数")
        print("✅ 无重复配置调用 - 遵循最佳实践")
        
        print("\n🚀 现在可以访问应用查看宽屏效果")
        print("💡 宽屏布局效果:")
        print("   - 页面内容将占用整个浏览器宽度")
        print("   - 不再限制在居中的固定宽度列中")
        print("   - 侧边栏保持展开状态")
        print("   - 更好地利用大屏幕空间")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
