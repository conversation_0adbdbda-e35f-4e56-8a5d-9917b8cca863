"""
通义千问API客户端 - 专注于纯净的API调用
"""

import requests
import time
from typing import Dict, Any, Optional
from ..llm.base import BaseLLM, LLMRequest, LLMResponse
from ..utils.config import TongyiConfig
from ..utils.logger import get_llm_logger
from ..utils.validators import validate_api_key


class TongyiQianwenClient(BaseLLM):
    """
    通义千问API客户端
    
    专注于与通义千问API的通信，不包含业务逻辑。
    遵循Streamlit最佳实践，在独立模块中定义类。
    """
    
    # 推荐的模型配置
    RECOMMENDED_MODELS = {
        "qwen-plus": {
            "name": "qwen-plus",
            "description": "通用模型，平衡性能",
            "max_tokens": 2000,
            "temperature": 0.1
        },
        "qwen-turbo": {
            "name": "qwen-turbo", 
            "description": "快速响应模型",
            "max_tokens": 1500,
            "temperature": 0.1
        },
        "qwen-max": {
            "name": "qwen-max",
            "description": "最强性能模型",
            "max_tokens": 6000,
            "temperature": 0.1
        }
    }
    
    def __init__(self, config: TongyiConfig):
        """
        初始化通义千问客户端
        
        Args:
            config: 通义千问配置对象
        """
        super().__init__(config.to_dict())
        
        self.api_key = config.api_key
        self.model = config.model
        self.temperature = config.temperature
        self.max_tokens = config.max_tokens
        self.timeout = config.timeout
        self.retry_attempts = config.retry_attempts
        self.retry_delay = config.retry_delay
        
        # API配置
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
        # 日志记录器
        self.logger = get_llm_logger() if config.enable_logging else None
        
        # 验证配置
        if not self.validate_config():
            raise ValueError("通义千问配置无效")
    
    @property
    def type(self) -> str:
        """返回LLM类型标识"""
        return "tongyi_qianwen"
    
    @property
    def model_name(self) -> str:
        """返回模型名称"""
        return self.model
    
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        is_valid, error_msg = validate_api_key(self.api_key)
        if not is_valid:
            if self.logger:
                self.logger.error(f"API密钥验证失败: {error_msg}")
            return False
        
        if self.model not in self.RECOMMENDED_MODELS:
            if self.logger:
                self.logger.warning(f"使用非推荐模型: {self.model}")
        
        return True
    
    def _call_api(self, request: LLMRequest) -> LLMResponse:
        """
        调用通义千问API的核心实现
        
        Args:
            request: LLM请求对象
            
        Returns:
            LLM响应对象
        """
        # 构建提示词
        prompt = self._build_prompt(request.instruction, request.context)
        
        # 构建请求数据
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        
        # 记录请求
        if self.logger:
            self.logger.info(f"调用通义千问API - 模型: {self.model}, tokens: {request.max_tokens}")
        
        # 执行API调用（带重试机制）
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(
                    self.base_url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    
                    # 提取token使用信息
                    tokens_used = result.get('usage', {}).get('total_tokens')
                    
                    if self.logger:
                        self.logger.info(f"API调用成功 - tokens: {tokens_used}")
                    
                    return LLMResponse(
                        content=content,
                        model=self.model,
                        tokens_used=tokens_used,
                        metadata={
                            'status_code': response.status_code,
                            'attempt': attempt + 1
                        }
                    )
                else:
                    error_msg = f"API调用失败 - 状态码: {response.status_code}"
                    if self.logger:
                        self.logger.error(error_msg)
                    
                    if attempt == self.retry_attempts - 1:
                        return LLMResponse(
                            content="print('API调用失败')",
                            model=self.model,
                            metadata={'error': error_msg}
                        )
            
            except requests.exceptions.Timeout:
                error_msg = f"API调用超时 (尝试 {attempt + 1}/{self.retry_attempts})"
                if self.logger:
                    self.logger.warning(error_msg)
                
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay)
                else:
                    return LLMResponse(
                        content="print('API调用超时')",
                        model=self.model,
                        metadata={'error': error_msg}
                    )
            
            except Exception as e:
                error_msg = f"API调用异常: {str(e)}"
                if self.logger:
                    self.logger.error(error_msg)
                
                return LLMResponse(
                    content=f"print('API异常: {str(e)}')",
                    model=self.model,
                    metadata={'error': error_msg}
                )
        
        # 不应该到达这里
        return LLMResponse(
            content="print('未知错误')",
            model=self.model,
            metadata={'error': '未知错误'}
        )
    
    def _build_prompt(self, instruction: str, context: str) -> str:
        """
        构建基础提示词

        Args:
            instruction: 用户指令
            context: 数据上下文

        Returns:
            构建好的提示词
        """
        prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{context}

用户指令: {instruction}

要求:
1. 只返回可执行的Python代码，不要解释
2. 数据已经加载在变量 df 中，不要重新读取文件
3. 使用pandas进行数据处理，直接使用 df 变量
4. 代码要简洁高效，确保语法正确
5. 严格按照Streamlit官方API使用图表组件：

   📊 柱状图 - 使用 st.bar_chart():
   - 数据格式：DataFrame或Series
   - 参数：data, x=None, y=None, color=None, horizontal=False
   - 示例：st.bar_chart(data, x="category", y="value")

   📈 折线图 - 使用 st.line_chart():
   - 数据格式：DataFrame或Series
   - 参数：data, x=None, y=None, color=None
   - 示例：st.line_chart(data, x="date", y="value")

   📊 散点图 - 使用 st.scatter_chart():
   - 数据格式：DataFrame
   - 参数：data, x=None, y=None, color=None, size=None
   - 示例：st.scatter_chart(data, x="x_col", y="y_col", color="category")

   📊 面积图 - 使用 st.area_chart():
   - 数据格式：DataFrame或Series
   - 参数：data, x=None, y=None, color=None
   - 示例：st.area_chart(data, x="date", y="value")

   🥧 饼图 - 使用 plotly + st.plotly_chart():
   - Streamlit原生不支持饼图，必须使用plotly
   - 示例：
     import plotly.express as px
     fig = px.pie(data, values="value", names="category")
     st.plotly_chart(fig, use_container_width=True)

6. 禁止使用matplotlib (plt.*)，必须使用Streamlit原生图表
7. 使用st.write()显示文本结果，st.dataframe()显示表格数据

请生成代码:"""

        return prompt
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        model_info = self.RECOMMENDED_MODELS.get(self.model, {})
        return {
            'model': self.model,
            'type': self.type,
            'description': model_info.get('description', '未知模型'),
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
        }
