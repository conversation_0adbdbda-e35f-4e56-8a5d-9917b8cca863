#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_import():
    """测试配置导入"""
    print("🔍 测试配置导入...")
    
    try:
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        
        # 创建集成实例（不启用日志以避免Streamlit警告）
        integration = StreamlitLLMIntegration(enable_logging=False)
        print("✅ StreamlitLLMIntegration 创建成功")
        
        # 测试自动初始化方法（不会真正初始化，只是测试方法调用）
        try:
            result = integration.auto_initialize_llm()
            print(f"✅ auto_initialize_llm 方法调用成功，返回: {result}")
        except Exception as e:
            print(f"❌ auto_initialize_llm 方法调用失败: {e}")
            return False
        
        # 测试自动文件加载方法
        try:
            integration.auto_load_recent_file()
            print("✅ auto_load_recent_file 方法调用成功")
        except Exception as e:
            print(f"❌ auto_load_recent_file 方法调用失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入测试失败: {e}")
        return False

def test_main_app_import():
    """测试主应用导入"""
    print("\n🔍 测试主应用导入...")
    
    try:
        from app.main import main
        print("✅ 主应用导入成功")
        return True
    except Exception as e:
        print(f"❌ 主应用导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试配置修复\n")
    
    tests = [
        ("配置导入", test_config_import),
        ("主应用导入", test_main_app_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 配置修复成功！应用应该可以正常启动了。")
        print("\n🚀 现在可以访问: http://localhost:8501")
    else:
        print("⚠️ 部分测试失败，可能还有其他问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
